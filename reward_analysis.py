import random
import matplotlib.pyplot as plt

def calculate_reward_probability(M):
    """Beregn reward-sannsynlighet basert på M"""
    if M <= 3:
        return M * 0.2
    else:  # M == 4 or M == 5
        return 0.6 - ((M - 3) * 0.2)

def analyze_reward_probabilities():
    """Analyser reward-sannsynligheter for alle M-verdier"""
    print("=== REWARD-SANNSYNLIGHETER ===")
    print("M-verdi | Reward % | Penalty % | Forklaring")
    print("-" * 50)
    
    for M in range(6):
        reward_prob = calculate_reward_probability(M)
        penalty_prob = 1 - reward_prob
        
        if M <= 3:
            formula = f"M * 0.2 = {M} * 0.2"
        else:
            formula = f"0.6 - ((M-3) * 0.2) = 0.6 - (({M}-3) * 0.2)"
        
        print(f"M={M}     | {reward_prob:.1%}     | {penalty_prob:.1%}      | {formula}")
    
    print("\n=== HVORFOR TREKKER DETTE MOT M=3? ===")
    print("M=3 har HØYEST reward-sannsynlighet (60%)")
    print("Alle andre M-verdier har LAVERE reward-sannsynlighet")
    print("→ Systemet 'belønnes' mest når M=3")

def demonstrate_convergence_force():
    """Demonstrer hvordan hver M-verdi påvirker systemet"""
    print("\n=== KONVERGENS-KREFTER ===")
    
    for M in range(6):
        reward_prob = calculate_reward_probability(M)
        
        print(f"\nHvis M={M} (reward: {reward_prob:.1%}):")
        
        if M < 3:
            print(f"  → {reward_prob:.1%} sjanse for reward (øker tilstander)")
            print(f"  → {1-reward_prob:.1%} sjanse for penalty (senker tilstander)")
            print(f"  → MER penalty enn reward → Flere automata flytter mot JA")
            print(f"  → M øker mot 3")
        elif M == 3:
            print(f"  → {reward_prob:.1%} sjanse for reward (øker tilstander)")
            print(f"  → {1-reward_prob:.1%} sjanse for penalty (senker tilstander)")
            print(f"  → MER reward enn penalty → STABIL TILSTAND!")
        else:  # M > 3
            print(f"  → {reward_prob:.1%} sjanse for reward (øker tilstander)")
            print(f"  → {1-reward_prob:.1%} sjanse for penalty (senker tilstander)")
            print(f"  → MER penalty enn reward → Flere automata flytter mot NEI")
            print(f"  → M synker mot 3")

def simulate_single_step_effects():
    """Simuler effekten av én iterasjon for hver M-verdi"""
    print("\n=== SIMULERING: EFFEKT AV ÉN ITERASJON ===")
    
    n_simulations = 10000
    
    for M in range(6):
        reward_prob = calculate_reward_probability(M)
        
        # Simuler mange iterasjoner for denne M-verdien
        rewards = 0
        penalties = 0
        
        for _ in range(n_simulations):
            if random.random() < reward_prob:
                rewards += 1
            else:
                penalties += 1
        
        net_effect = rewards - penalties
        direction = "→ M øker" if net_effect > 0 else "→ M synker" if net_effect < 0 else "→ M stabil"
        
        print(f"M={M}: {rewards} rewards, {penalties} penalties, netto: {net_effect:+d} {direction}")

def explain_mathematical_intuition():
    """Forklar den matematiske intuisjonen"""
    print("\n=== MATEMATISK INTUISJON ===")
    
    print("Formelen skaper en 'dal' med M=3 som bunn:")
    print()
    print("Reward-sannsynlighet:")
    print("  M=0: 0%")
    print("  M=1: 20%")
    print("  M=2: 40%")
    print("  M=3: 60%  ← TOPP!")
    print("  M=4: 40%")
    print("  M=5: 20%")
    print()
    print("Dette ligner en 'omvendt U-kurve' med topp ved M=3")
    print("Systemet 'ruller' naturlig ned mot bunnen av dalen (M=3)")

if __name__ == "__main__":
    analyze_reward_probabilities()
    demonstrate_convergence_force()
    simulate_single_step_effects()
    explain_mathematical_intuition()
