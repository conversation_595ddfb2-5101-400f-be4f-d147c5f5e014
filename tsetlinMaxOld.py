import random


class TsetlinAutomaton:
    def __init__(self, n_states=4):
        self.n_states = n_states
        self.state = random.randint(1, n_states)  # random state mellom 1 og 4

    def action(self):
        # States 1,2 gir 1 (Ja), States 3,4 gir 0 (<PERSON><PERSON>)
        return 1 if self.state <= self.n_states // 2 else 0

    def reward(self):
        # hvis state er mindre enn 4, ta (state + 1) --> reward mot 1 / Yes
        if self.state < self.n_states:
            self.state += 1

    def penalty(self):
        # hvis state er større enn 1, ta (state - 1) --> penalty mot 0 / No
        if self.state > 1:
            self.state -= 1


ta1 = TsetlinAutomaton()
ta2 = TsetlinAutomaton()
ta3 = TsetlinAutomaton()
ta4 = TsetlinAutomaton()
ta5 = TsetlinAutomaton()

M_history = []

for i in range(100000):  # iterasjoner
    # pluss actions til en M
    M = ta1.action() + ta2.action() + ta3.action() + ta4.action() + ta5.action()
    actions = [ta1.action(), ta2.action(), ta3.action(), ta4.action(), ta5.action()]
    M_history.append(M)  # lagre alle M i en liste

    if M == 0 or M == 1 or M == 2 or M == 3:
        if random.random() < (M * 0.2):
            ta1.reward()
        else:
            ta1.penalty()
        if random.random() < (M * 0.2):
            ta2.reward()
        else:
            ta2.penalty()
        if random.random() < (M * 0.2):
            ta3.reward()
        else:
            ta3.penalty()
        if random.random() < (M * 0.2):
            ta4.reward()
        else:
            ta4.penalty()
        if random.random() < (M * 0.2):
            ta5.reward()
        else:
            ta5.penalty()
    if M == 4 or M == 5:
        if random.random() < (0.6 - ((M - 3) * 0.2)):
            ta1.reward()
        else:
            ta1.penalty()
        if random.random() < (0.6 - ((M - 3) * 0.2)):
            ta2.reward()
        else:
            ta2.penalty()
        if random.random() < (0.6 - ((M - 3) * 0.2)):
            ta3.reward()
        else:
            ta3.penalty()
        if random.random() < (0.6 - ((M - 3) * 0.2)):
            ta4.reward()
        else:
            ta4.penalty()
        if random.random() < (0.6 - ((M - 3) * 0.2)):
            ta5.reward()
        else:
            ta5.penalty()

# Statistics
print(f"Average M over all iterations: {sum(M_history) / len(M_history):.2f}")
print(f"Average M (last 100 iterations): {sum(M_history[-100:]) / 100:.2f}")
print(f"M value distribution: {[M_history.count(i) for i in range(6)]}")
print(f"Most common M value: {max(set(M_history), key=M_history.count)}")