import random


class TsetlinAutomaton:
    def __init__(self, n_states=4):
        self.n_states = n_states
        self.state = random.randint(1, n_states)  # random state mellom 1 og 4

    def action(self):
        # States 1,2 gir 1 (Ja), States 3,4 gir 0 (<PERSON><PERSON>)
        return 1 if self.state <= self.n_states // 2 else 0

    def reward(self):
        # hvis state er mindre enn 4, ta (state + 1) --> reward mot 1 / Yes
        if self.state < self.n_states:
            self.state += 1

    def penalty(self):
        # hvis state er større enn 1, ta (state - 1) --> penalty mot 0 / No
        if self.state > 1:
            self.state -= 1


automata = [TsetlinAutomaton() for _ in range(5)]

M_history = []

for i in range(10000):  # iterasjoner
    # pluss actions til en M
    M = sum(automaton.action() for automaton in automata)

    M_history.append(M)  # lagre alle M i en liste

    if M == 0 or M == 1 or M == 2 or M == 3:
        for automaton in automata:
            if random.random() < (M * 0.2):
                automaton.reward()
            else:
                automaton.penalty()

    if M == 4 or M == 5:
        for automaton in automata:
            if random.random() < (0.6 - ((M - 3) * 0.2)):
                automaton.reward()
            else:
                automaton.penalty()


# Statistics
print(f"Average M over all iterations: {sum(M_history) / len(M_history):.2f}")
print(f"Average M (last 100 iterations): {sum(M_history[-100:]) / 100:.2f}")
print(f"M value distribution: {[M_history.count(i) for i in range(6)]}")
print(f"Most common M value: {max(set(M_history), key=M_history.count)}")
