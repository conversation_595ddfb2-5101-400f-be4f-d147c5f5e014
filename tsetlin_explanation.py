import random
import matplotlib.pyplot as plt


class TsetlinAutomaton:
    def __init__(self, n_states=4):
        self.n_states = n_states
        self.state = random.randint(1, n_states)
        self.state_history = [self.state]  # For å spore endringer

    def action(self):
        return 1 if self.state <= self.n_states // 2 else 0

    def reward(self):
        if self.state < self.n_states:
            self.state += 1
        self.state_history.append(self.state)

    def penalty(self):
        if self.state > 1:
            self.state -= 1
        self.state_history.append(self.state)

    def get_state_history(self):
        return self.state_history


def calculate_reward_probability(M):
    """Beregn reward-sannsynlighet basert på M"""
    if M <= 3:
        return M * 0.2
    else:  # M == 4 or M == 5
        return 0.6 - ((M - 3) * 0.2)


def run_detailed_simulation(iterations=1000):
    """<PERSON><PERSON><PERSON><PERSON> simulering med detaljert logging"""
    automata = [TsetlinAutomaton() for _ in range(5)]
    M_history = []
    reward_prob_history = []
    
    print("=== TSETLIN ALGORITME - STEG FOR STEG ===\n")
    
    for i in range(iterations):
        # 1. Få actions fra alle automata
        actions = [automaton.action() for automaton in automata]
        M = sum(actions)
        
        # 2. Beregn reward probability
        reward_prob = calculate_reward_probability(M)
        
        # 3. Lagre historie
        M_history.append(M)
        reward_prob_history.append(reward_prob)
        
        # 4. Vis detaljer for første 10 iterasjoner
        if i < 10:
            states = [automaton.state for automaton in automata]
            print(f"Iterasjon {i+1}:")
            print(f"  Tilstander: {states}")
            print(f"  Actions:    {actions}")
            print(f"  M (sum):    {M}")
            print(f"  Reward prob: {reward_prob:.1%}")
        
        # 5. Oppdater alle automata
        for automaton in automata:
            if random.random() < reward_prob:
                automaton.reward()
                if i < 10:
                    print(f"    Automaton belønnet: {automaton.state-1} → {automaton.state}")
            else:
                automaton.penalty()
                if i < 10:
                    print(f"    Automaton straffet: {automaton.state+1} → {automaton.state}")
        
        if i < 10:
            print()
    
    return automata, M_history, reward_prob_history


def analyze_convergence(M_history):
    """Analyser hvordan algoritmen konvergerer"""
    print("=== KONVERGENS-ANALYSE ===")
    
    # Beregn glidende gjennomsnitt
    window_size = 100
    moving_avg = []
    for i in range(len(M_history)):
        start = max(0, i - window_size + 1)
        moving_avg.append(sum(M_history[start:i+1]) / (i - start + 1))
    
    print(f"Første 100 iterasjoner - Gjennomsnitt M: {sum(M_history[:100])/100:.2f}")
    print(f"Siste 100 iterasjoner - Gjennomsnitt M: {sum(M_history[-100:])/100:.2f}")
    print(f"Total gjennomsnitt M: {sum(M_history)/len(M_history):.2f}")
    
    # Distribusjon
    distribution = [M_history.count(i) for i in range(6)]
    print(f"M-verdi distribusjon: {distribution}")
    print(f"Mest vanlige M-verdi: {max(set(M_history), key=M_history.count)}")
    
    return moving_avg


def explain_why_converges_to_3():
    """Forklarer hvorfor algoritmen konvergerer mot M=3"""
    print("\n=== HVORFOR KONVERGERER ALGORITMEN MOT M=3? ===")
    
    print("Reward-sannsynligheter for hver M-verdi:")
    for M in range(6):
        prob = calculate_reward_probability(M)
        print(f"M={M}: {prob:.1%} reward, {1-prob:.1%} penalty")
    
    print("\nLogikken:")
    print("• M=3 har høyest reward-sannsynlighet (60%)")
    print("• M=2 og M=4 har lavere reward-sannsynlighet (40%)")
    print("• M=0,1,5 har enda lavere reward-sannsynlighet")
    print("• Derfor 'trekkes' systemet mot M=3 over tid")
    
    print("\nDette er et eksempel på 'reinforcement learning':")
    print("• Automata lærer gjennom trial-and-error")
    print("• De belønnes for å bidra til ønskede utfall")
    print("• Over tid konvergerer de mot optimal strategi")


if __name__ == "__main__":
    # Kjør detaljert simulering
    automata, M_history, reward_prob_history = run_detailed_simulation(1000)
    
    # Analyser resultater
    moving_avg = analyze_convergence(M_history)
    
    # Forklar konvergens
    explain_why_converges_to_3()
    
    # Vis final tilstander
    print(f"\n=== FINALE TILSTANDER ===")
    final_states = [automaton.state for automaton in automata]
    final_actions = [automaton.action() for automaton in automata]
    print(f"Finale tilstander: {final_states}")
    print(f"Finale actions: {final_actions}")
    print(f"Final M: {sum(final_actions)}")
